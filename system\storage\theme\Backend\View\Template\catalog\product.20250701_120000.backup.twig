<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto overflow-x-hidden bg-gray-50" style="padding-bottom: 200px">

    <!-- Product Header -->
    <div class="bg-white border-b border-gray-200 px-6 py-4">
        <div class="flex flex-col md:flex-row md:items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">Продукти</h1>
                <p class="text-gray-500 mt-1">Управление на всички продукти в магазина</p>
            </div>
            <a href="{{ add_new_url }}" class="mt-4 md:mt-0 px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center !rounded-button">
                <div class="w-5 h-5 flex items-center justify-center mr-2">
                    <i class="ri-add-line"></i>
                </div>
                <span>Нов продукт</span>
            </a>
        </div>
    </div>
    <!-- Filters -->
    <div class="bg-white border-b border-gray-200 px-6 py-3">
        <div class="flex flex-wrap items-center gap-4">
            <button id="filter-btn" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center !rounded-button">
                <div class="w-5 h-5 flex items-center justify-center mr-2">
                    <i class="ri-filter-3-line"></i>
                </div>
                <span>Филтър</span>
            </button>
            <div class="w-full md:w-auto">
                <div class="relative">
                    <!-- Filter Modal -->
                    <div id="filter-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
                        <div class="bg-white rounded-lg w-full max-w-md mx-4">
                            <div class="flex justify-between items-center p-6 border-b border-gray-200">
                                <h3 class="text-lg font-semibold text-gray-800">Филтър</h3>
                                <button id="close-filter" class="text-gray-400 hover:text-gray-500">
                                    <div class="w-6 h-6 flex items-center justify-center">
                                        <i class="ri-close-line"></i>
                                    </div>
                                </button>
                            </div>
                            <div class="p-6">
                                <form id="filter-form" class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Категория</label>
                                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" placeholder="Въведете категория">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Име на продукт</label>
                                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" placeholder="Въведете име на продукт">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Модел</label>
                                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" placeholder="Въведете модел">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Цена</label>
                                        <div class="flex space-x-2">
                                            <input type="number" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" placeholder="От">
                                            <input type="number" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" placeholder="До">
                                        </div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Статус</label>
                                        <select class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm pr-8">
                                            <option value="">Всички</option>
                                            <option value="active">Активен</option>
                                            <option value="inactive">Неактивен</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Период</label>
                                        <div class="flex space-x-2">
                                            <input type="date" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm">
                                            <input type="date" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm">
                                        </div>
                                    </div>
                                    <div class="flex justify-end space-x-2 mt-6">
                                        <button type="button" id="reset-filter" class="px-4 py-2 border border-gray-300 rounded-button text-gray-700 hover:bg-gray-50 text-sm whitespace-nowrap">Изчисти</button>
                                        <button type="submit" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 text-sm whitespace-nowrap">Приложи филтър</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div id="sort-dropdown" class="hidden absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded shadow-lg">
                        <ul class="py-1">
                            {% for sort_option in sort_options %}
                            <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer" data-sort="{{ sort_option.value }}">{{ sort_option.text }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            </div>
            <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-600">Активни:</span>
                <label class="toggle-switch">
                    <input type="checkbox" id="filter-active" {% if filter_active %}checked{% endif %}>
                    <span class="toggle-slider"></span>
                </label>
            </div>
            <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-600">Промоции:</span>
                <label class="toggle-switch">
                    <input type="checkbox" id="filter-special" {% if filter_special %}checked{% endif %}>
                    <span class="toggle-slider"></span>
                </label>
            </div>
            <div class="flex items-center space-x-2 ml-auto">
                <button class="w-10 h-10 flex items-center justify-center rounded-button text-gray-600 hover:bg-gray-200 {% if view_type == 'grid' %} bg-gray-100 active {% endif %}" id="grid-view">
                    <div class="w-5 h-5 flex items-center justify-center">
                        <i class="ri-grid-line"></i>
                    </div>
                </button>
                <button class="w-10 h-10 flex items-center justify-center rounded-button text-gray-600 hover:bg-gray-200 {% if view_type == 'list' %} bg-gray-100 active {% endif %}" id="list-view">
                    <div class="w-5 h-5 flex items-center justify-center">
                        <i class="ri-list-check"></i>
                    </div>
                </button>
            </div>
        </div>
    </div>

    <!-- Products Grid -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6 p-6" id="products-grid">
        {% if products %}
            {% for product in products %}
            <!-- Product Card -->
            <div class="product-card bg-white rounded shadow hover:shadow-md transition-all">
                <div class="relative">
                    <a href="{{ product.edit }}">
                    <div class="product-image-container w-full h-48 rounded-t bg-gray-100 flex items-center justify-center relative" data-product-id="{{ product.product_id }}" data-width="400" data-height="300">
                        <div class="product-image-placeholder">
                            <div class="w-10 h-10 text-gray-300 animate-spin">
                                <i class="ri-loader-4-line ri-2x"></i>
                            </div>
                        </div>
                        <div class="product-image-error hidden">
                            <div class="w-10 h-10 text-red-500">
                                <i class="ri-error-warning-line ri-2x"></i>
                            </div>
                            <p class="text-sm text-red-500 mt-2">Грешка при зареждане</p>
                        </div>
                        <img src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" alt="{{ product.name }}" class="w-full h-48 object-cover object-top rounded-t absolute inset-0 opacity-0 transition-opacity duration-300">
                    </div>
                    </a>
                    <div class="absolute top-3 right-3 flex space-x-2">
                        {% if product.status %}
                        <span class="px-2 py-1 bg-green-500 text-white text-xs rounded-full">Активен</span>
                        {% else %}
                        <span class="px-2 py-1 bg-yellow-500 text-white text-xs rounded-full">Неактивен</span>
                        {% endif %}

                        {% if product.special %}
                        <span class="px-2 py-1 bg-red-500 text-white text-xs rounded-full">{{ product.discount_percent }}</span>
                        {% endif %}
                    </div>
                </div>
                <div class="p-4">
                    <div class="flex justify-between items-start mb-2">
                        <div>
                            <h3 class="font-medium text-gray-800">{{ product.name }}</h3>
                            <p class="text-sm text-gray-500">{{ product.category }}</p>
                             <p class="font-bold text-gray-800">{{ product.price|number_format(2, '.', ',') }} лв.</p>
                            {% if product.special %}
                            <p class="text-sm text-gray-500 line-through">{{ product.old_price|number_format(2, '.', ',') }} лв.</p>
                            {% endif %}
                        </div>
                    </div>
                    <div class="flex items-center justify-between mt-4">
                        <span class="text-sm text-gray-500">Код: {{ product.model }}</span>
                        <div class="card-actions flex space-x-1">
                            <a href="{{ product.edit }}" class="w-8 h-8 flex items-center justify-center text-primary hover:bg-primary/10 rounded-full">
                                <div class="w-5 h-5 flex items-center justify-center">
                                    <i class="ri-edit-line"></i>
                                </div>
                            </a>
                              <div class="relative">
                                <button class="w-8 h-8 flex items-center justify-center text-gray-500 hover:bg-gray-100 rounded-full product-actions-btn">
                                    <div class="w-5 h-5 flex items-center justify-center">
                                        <i class="ri-more-2-line"></i>
                                    </div>
                                </button>
                                <div class="product-actions-dropdown hidden absolute right-0 bottom-full mb-2 bg-white border border-gray-200 rounded shadow-lg z-10 w-40">
                                    <ul class="py-1">
                                        <li>
                                            <a href="#" class="px-4 py-2 hover:bg-gray-100 flex items-center text-sm duplicate-product" data-product-id="{{ product.product_id }}">
                                                <i class="ri-file-copy-line mr-2"></i> Дублиране
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#" class="px-4 py-2 hover:bg-gray-100 flex items-center text-sm delete-product" data-product-id="{{ product.product_id }}">
                                                <i class="ri-delete-bin-5-line text-red-500 mr-2"></i> Изтриване
                                            </a>
                                        </li>

                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-span-full text-center py-8">
                <div class="w-16 h-16 mx-auto mb-4 text-gray-300">
                    <i class="ri-shopping-bag-line ri-3x"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-800 mb-1">Няма намерени продукти</h3>
                <p class="text-gray-500">Все още няма добавени продукти или няма продукти, отговарящи на зададените критерии.</p>
                <a href="{{ add_new_url }}" class="mt-4 inline-flex items-center px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90">
                    <i class="ri-add-line mr-2"></i> Нов продукт
                </a>
            </div>
        {% endif %}
    </div>

    <!-- Pagination -->
    <div class="p-6">
    {{ pagination_html|raw }}
    </div>
</main>

<script type="text/javascript">
document.addEventListener('DOMContentLoaded', function() {
    // Filter modal functionality
    const filterBtn = document.getElementById('filter-btn');
    const filterModal = document.getElementById('filter-modal');
    const closeFilter = document.getElementById('close-filter');
    const filterForm = document.getElementById('filter-form');
    const resetFilter = document.getElementById('reset-filter');

    filterBtn.addEventListener('click', function() {
        filterModal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    });

    closeFilter.addEventListener('click', function() {
        filterModal.classList.add('hidden');
        document.body.style.overflow = 'auto';
    });

    filterModal.addEventListener('click', function(e) {
        if (e.target === filterModal) {
            filterModal.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }
    });

    filterForm.addEventListener('submit', function(e) {
        e.preventDefault();
        // Add your filter logic here
        filterModal.classList.add('hidden');
        document.body.style.overflow = 'auto';
    });

    resetFilter.addEventListener('click', function() {
        filterForm.reset();
    });

    // View toggle
    const gridView = document.getElementById('grid-view');
    const listView = document.getElementById('list-view');
    const productsContainer = document.getElementById('products-grid');

    gridView.addEventListener('click', function() {
        productsContainer.classList.remove('grid-cols-1');
        productsContainer.classList.add('grid-cols-1', 'sm:grid-cols-2', 'lg:grid-cols-3', 'xl:grid-cols-4', '2xl:grid-cols-5');
        gridView.classList.add('active', 'bg-gray-100');
        listView.classList.remove('active', 'bg-gray-100');

        const productImages = document.querySelectorAll('.product-card img');
        productImages.forEach(img => {
            img.classList.remove('w-24', 'h-24');
            img.classList.add('w-full', 'h-48');
        });
    });

    listView.addEventListener('click', function() {
        productsContainer.classList.remove('sm:grid-cols-2', 'lg:grid-cols-3', 'xl:grid-cols-4', '2xl:grid-cols-5');
        productsContainer.classList.add('grid-cols-1');
        listView.classList.add('active', 'bg-gray-100');
        gridView.classList.remove('active', 'bg-gray-100');

        const productImages = document.querySelectorAll('.product-card img');
        productImages.forEach(img => {
            img.classList.remove('w-full', 'h-48');
            img.classList.add('w-24', 'h-24');
        });
    });


    // Product actions dropdown
    const productActionsBtns = document.querySelectorAll('.product-actions-btn');
    productActionsBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.stopPropagation();
            const dropdown = this.nextElementSibling;
            dropdown.classList.toggle('hidden');
        });
    });

    // Close product actions dropdown when clicking outside
    document.addEventListener('click', function() {
        document.querySelectorAll('.product-actions-dropdown').forEach(dropdown => {
            dropdown.classList.add('hidden');
        });
    });

    // Delete product confirmation
    const deleteButtons = document.querySelectorAll('.delete-product');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.getAttribute('data-product-id');
            if (confirm('Сигурни ли сте, че искате да изтриете този продукт?')) {
                window.location = '{{ delete_url|raw }}'.replace('PRODUCT_ID', productId);
            }
        });
    });

    // Toggle filters
    const filterActiveCheckbox = document.getElementById('filter-active');
    if (filterActiveCheckbox) {
        filterActiveCheckbox.addEventListener('change', function() {
            window.location = '{{ filter_active_url|raw }}'.replace('FILTER_VALUE', this.checked ? '1' : '0');
        });
    }

    const filterSpecialCheckbox = document.getElementById('filter-special');
    if (filterSpecialCheckbox) {
        filterSpecialCheckbox.addEventListener('change', function() {
            window.location = '{{ filter_special_url|raw }}'.replace('FILTER_VALUE', this.checked ? '1' : '0');
        });
    }

    // AJAX зареждане на изображения
    function loadProductImages(isScrollEvent = false) {
        // Получаваме текущата позиция на скролиране
        // Тъй като скролирането се случва в main контейнера, а не в window
        const mainContainer = document.querySelector('main.overflow-y-auto');
        const scrollTop = mainContainer ? mainContainer.scrollTop : (window.pageYOffset || document.documentElement.scrollTop);

        const imageContainers = document.querySelectorAll('.product-image-container');
        let loadedCount = 0;

        imageContainers.forEach(container => {
            const productId = container.getAttribute('data-product-id');
            const width = container.getAttribute('data-width');
            const height = container.getAttribute('data-height');
            const img = container.querySelector('img');

            // Проверка дали изображението вече е заредено или е имало грешка при зареждането
            // Проверяваме дали src атрибутът съдържа реален URL, а не placeholder
            const placeholderBase64 = "data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==";

            // Проверяваме дали изображението вече е заредено
            if (img.src && img.src !== placeholderBase64 && img.src !== window.location.href) {
                return;
            }

            // Проверяваме дали контейнерът е маркиран като такъв с грешка при зареждане
            if (container.hasAttribute('data-load-error')) {
                return;
            }

            // Проверка дали контейнерът е видим във viewport
            const rect = container.getBoundingClientRect();
            const viewportHeight = window.innerHeight || document.documentElement.clientHeight;

            // Намираме main контейнера, който има скролиране
            const mainContainer = document.querySelector('main.overflow-y-auto');

            // Изчисляваме позицията спрямо main контейнера, ако съществува
            let relativeTop = rect.top;
            let relativeBottom = rect.bottom;

            if (mainContainer) {
                // Изчисляваме позицията спрямо main контейнера
                const mainRect = mainContainer.getBoundingClientRect();
                relativeTop = rect.top - mainRect.top;
                relativeBottom = rect.bottom - mainRect.top;
            }

            // При първоначално зареждане проверяваме дали елементът е наистина видим или много близо
            // При скролиране използваме по-голям буфер
            let isVisible;

            if (!isScrollEvent) {
                // При първоначално зареждане - изключително строга проверка
                // Елементът трябва да бъде видим или много близо до видимата област
                if (mainContainer) {
                    // Проверка спрямо main контейнера
                    isVisible = (
                        relativeTop >= 0 && // Елементът трябва да бъде под горната граница на контейнера
                        relativeTop < mainContainer.clientHeight && // Горната част на елемента трябва да бъде във видимата част на контейнера
                        relativeBottom > 0 // Долната част на елемента трябва да бъде под горната граница на контейнера
                    );

                    // Допълнителна проверка - дали елементът е наистина във видимата част на контейнера
                    const isInViewport = (
                        relativeTop >= 0 &&
                        relativeBottom <= mainContainer.clientHeight
                    );


                } else {
                    // Стандартна проверка спрямо viewport
                    isVisible = (
                        rect.top >= 0 && // Елементът трябва да бъде под горната граница на екрана
                        rect.top < viewportHeight && // Горната част на елемента трябва да бъде във viewport
                        rect.bottom > 0 // Долната част на елемента трябва да бъде под горната граница на екрана
                    );

                    // Допълнителна проверка - дали елементът е наистина във viewport
                    const isInViewport = (
                        rect.top >= 0 &&
                        rect.bottom <= viewportHeight
                    );


                }
            } else {
                // При скролиране - много либерална проверка
                // Проверяваме дали елементът е близо до видимата област
                const buffer = 1000; // Увеличаваме буфера значително

                if (mainContainer) {
                    // Проверка спрямо main контейнера
                    isVisible = (
                        relativeTop < mainContainer.clientHeight + buffer &&
                        relativeBottom > -buffer
                    );


                } else {
                    // Стандартна проверка спрямо viewport
                    isVisible = (
                        rect.top < viewportHeight + buffer &&
                        rect.bottom > -buffer
                    );


                }
            }

            if (!isVisible) {
                return;
            }

            // Изображението е видимо и ще бъде заредено

            // Увеличаваме брояча на изображенията, които ще се заредят
            loadedCount++;

            // AJAX заявка за зареждане на изображението
            // Използваме абсолютен път до контролера
            const baseUrl = window.location.href.split('index.php')[0];

            // Извличане на user_token от URL адреса
            const urlParams = new URLSearchParams(window.location.search);
            const userToken = urlParams.get('user_token') || '';

            fetch(`${baseUrl}index.php?route=tool/image&user_token=${userToken}&product_id=${productId}&width=${width}&height=${height}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('HTTP error ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                // Проверка дали URL адресът на изображението е валиден
                if (data.success && data.image) {
                    // Проверка дали URL адресът е абсолютен
                    if (data.image.indexOf('http') !== 0) {
                        // Ако URL адресът не започва с http, добавяме базовия URL
                        const baseUrl = window.location.href.split('index.php')[0];
                        img.src = baseUrl + data.image.replace(/^\//, ''); // Премахваме водещата наклонена черта, ако има такава
                    } else {
                        img.src = data.image;
                    }

                    img.onload = function() {
                        img.classList.remove('opacity-0');
                        container.querySelector('.product-image-placeholder').style.display = 'none';
                        container.querySelector('.product-image-error').classList.add('hidden');
                    };

                    img.onerror = function() {
                        // При грешка зареждаме placeholder изображение и маркираме контейнера
                        img.src = 'image/no_image.png';
                        img.classList.remove('opacity-0');
                        container.querySelector('.product-image-placeholder').style.display = 'none';
                        container.querySelector('.product-image-error').classList.add('hidden');
                        // Маркираме контейнера, че е имало грешка при зареждане
                        container.setAttribute('data-load-error', 'true');
                    };
                } else {
                    // При грешка показваме placeholder изображението
                    // Проверка дали URL адресът е абсолютен
                    if (data.image && data.image.indexOf('http') !== 0) {
                        // Ако URL адресът не започва с http, добавяме базовия URL
                        const baseUrl = window.location.href.split('index.php')[0];
                        img.src = baseUrl + data.image.replace(/^\//, ''); // Премахваме водещата наклонена черта, ако има такава
                    } else if (data.image) {
                        img.src = data.image;
                    } else {
                        // Ако няма изображение, използваме стандартен placeholder
                        img.src = 'image/no_image.png';
                    }

                    img.onload = function() {
                        img.classList.remove('opacity-0');
                        container.querySelector('.product-image-placeholder').style.display = 'none';
                        container.querySelector('.product-image-error').classList.add('hidden');
                    };

                    img.onerror = function() {
                        // При грешка зареждаме базово placeholder изображение и маркираме контейнера
                        img.src = 'image/no_image.png';
                        img.classList.remove('opacity-0');
                        container.querySelector('.product-image-placeholder').style.display = 'none';
                        container.querySelector('.product-image-error').classList.add('hidden');
                        // Маркираме контейнера, че е имало грешка при зареждане
                        container.setAttribute('data-load-error', 'true');
                    };
                }
            })
            .catch(error => {
                // Показваме съобщение за грешка и маркираме контейнера
                container.querySelector('.product-image-placeholder').style.display = 'none';
                container.querySelector('.product-image-error').classList.remove('hidden');

                // Маркираме контейнера, че е имало грешка при зареждане
                container.setAttribute('data-load-error', 'true');

                // Скриваме изображението
                img.classList.add('opacity-0');
            });
        });


    }

    // ПОДХОД 5: Принудително зареждане на всички изображения след определено време
    // Това е резервен вариант, ако нито един от другите подходи не сработи
    setTimeout(function() {

        // Зареждаме всички изображения, независимо дали са видими или не
        const allImageContainers = document.querySelectorAll('.product-image-container');
        allImageContainers.forEach(container => {
            const productId = container.getAttribute('data-product-id');
            const width = container.getAttribute('data-width');
            const height = container.getAttribute('data-height');
            const img = container.querySelector('img');

            // Проверка дали изображението вече е заредено или е имало грешка при зареждането
            const placeholderBase64 = "data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==";

            // Проверяваме дали изображението вече е заредено
            if (img.src && img.src !== placeholderBase64 && img.src !== window.location.href) {
                return;
            }

            // Проверяваме дали контейнерът е маркиран като такъв с грешка при зареждане
            if (container.hasAttribute('data-load-error')) {
                return;
            }

            // AJAX заявка за зареждане на изображението
            const baseUrl = window.location.href.split('index.php')[0];
            const urlParams = new URLSearchParams(window.location.search);
            const userToken = urlParams.get('user_token') || '';

            fetch(`${baseUrl}index.php?route=tool/image&user_token=${userToken}&product_id=${productId}&width=${width}&height=${height}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('HTTP error ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                if (data.success && data.image) {
                    if (data.image.indexOf('http') !== 0) {
                        const baseUrl = window.location.href.split('index.php')[0];
                        img.src = baseUrl + data.image.replace(/^\//, '');
                    } else {
                        img.src = data.image;
                    }

                    img.onload = function() {
                        img.classList.remove('opacity-0');
                        container.querySelector('.product-image-placeholder').style.display = 'none';
                        container.querySelector('.product-image-error').classList.add('hidden');
                    };

                    img.onerror = function() {
                        img.src = 'image/no_image.png';
                        img.classList.remove('opacity-0');
                        container.querySelector('.product-image-placeholder').style.display = 'none';
                        container.querySelector('.product-image-error').classList.add('hidden');
                        // Маркираме контейнера, че е имало грешка при зареждане
                        container.setAttribute('data-load-error', 'true');
                    };
                } else {
                    if (data.image && data.image.indexOf('http') !== 0) {
                        const baseUrl = window.location.href.split('index.php')[0];
                        img.src = baseUrl + data.image.replace(/^\//, '');
                    } else if (data.image) {
                        img.src = data.image;
                    } else {
                        img.src = 'image/no_image.png';
                    }

                    img.onload = function() {
                        img.classList.remove('opacity-0');
                        container.querySelector('.product-image-placeholder').style.display = 'none';
                        container.querySelector('.product-image-error').classList.add('hidden');
                    };

                    img.onerror = function() {
                        img.src = 'image/no_image.png';
                        img.classList.remove('opacity-0');
                        container.querySelector('.product-image-placeholder').style.display = 'none';
                        container.querySelector('.product-image-error').classList.add('hidden');
                        // Маркираме контейнера, че е имало грешка при зареждане
                        container.setAttribute('data-load-error', 'true');
                    };
                }
            })
            .catch(error => {
                // Показваме съобщение за грешка и маркираме контейнера
                container.querySelector('.product-image-placeholder').style.display = 'none';
                container.querySelector('.product-image-error').classList.remove('hidden');

                // Маркираме контейнера, че е имало грешка при зареждане
                container.setAttribute('data-load-error', 'true');

                // Скриваме изображението
                img.classList.add('opacity-0');
            });
        });
    }, 3000); // 3 секунди след зареждане на страницата

    // Първоначално зареждане на видимите изображения
    loadProductImages(false);

    // Маркираме, че първоначалното зареждане е завършено
    window.initialLoadComplete = true;

    // ПОДХОД 4: Добавяме няколко различни обработчика на събитието за скролиране
    // Използваме throttle, за да ограничим броя на извикванията

    // Функция за throttle, която ще използваме за всички обработчици
    const throttledLoadImages = throttle(function() {
        if (window.initialLoadComplete) {
            loadProductImages(true);
        }
    }, 200); // Ограничаваме до едно извикване на 200ms

    // Намираме main контейнера, който има скролиране
    const mainContainer = document.querySelector('main.overflow-y-auto');

    // 1. Обработчик на main контейнера (най-важен)
    if (mainContainer) {
        mainContainer.addEventListener('scroll', throttledLoadImages);
    }

    // 2. Стандартен обработчик (резервен)
    window.addEventListener('scroll', throttledLoadImages);

    // 3. Обработчик с capture фаза (резервен)
    window.addEventListener('scroll', throttledLoadImages, true);

    // 4. Обработчик на document (резервен)
    document.addEventListener('scroll', throttledLoadImages);

    // 5. Обработчик на body (резервен)
    document.body.addEventListener('scroll', throttledLoadImages);

    // 6. Обработчик на wheel събитие (с отделен throttle)
    window.addEventListener('wheel', throttle(function() {
        if (window.initialLoadComplete) {
            loadProductImages(true);
        }
    }, 300));

    // 7. Обработчик на touchmove събитие (за мобилни устройства)
    window.addEventListener('touchmove', throttle(function() {
        if (window.initialLoadComplete) {
            loadProductImages(true);
        }
    }, 300));

    // Функция за дебаунс на събитията
    function debounce(func, wait) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(function() {
                func.apply(context, args);
            }, wait);
        };
    }

    // Функция за тротлинг на събитията
    function throttle(func, limit) {
        let inThrottle;
        return function() {
            const context = this;
            const args = arguments;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // Зареждане на изображения при скролиране
    // Използваме няколко различни подхода за следене на скролирането
    let lastScrollTop = 0;

    // ПОДХОД 1: Проверка за скролиране на всеки 200ms
    // Използваме по-дълъг интервал, за да намалим натоварването
    let lastCheckTime = Date.now();

    setInterval(function() {
        // Проверяваме дали е минало достатъчно време от последната проверка
        const now = Date.now();
        if (now - lastCheckTime < 200) {
            return;
        }

        lastCheckTime = now;

        // Получаваме текущата позиция на скролиране от main контейнера
        const mainContainer = document.querySelector('main.overflow-y-auto');
        const scrollTop = mainContainer ? mainContainer.scrollTop : (window.pageYOffset || document.documentElement.scrollTop);

        // Проверяваме дали има значителна промяна в позицията на скролиране
        if (Math.abs(scrollTop - lastScrollTop) > 10) {
            // Запазваме новата позиция
            lastScrollTop = scrollTop;

            // Проверяваме дали първоначалното зареждане е завършено
            if (window.initialLoadComplete) {
                loadProductImages(true);
            }
        }
    }, 200);

    // ПОДХОД 2: Използваме MutationObserver за следене на промени в DOM
    // Това може да улови някои случаи на скролиране, които не се засичат от стандартните събития
    // Но трябва да внимаваме да не създадем безкраен цикъл

    // Флаг, който показва дали в момента се обработват промени в DOM
    let isProcessingMutations = false;

    const observer = new MutationObserver(function(mutations) {
        // Проверяваме дали вече обработваме промени и дали първоначалното зареждане е завършено
        if (!isProcessingMutations && window.initialLoadComplete) {
            // Филтрираме мутациите, за да избегнем безкрайни цикли
            const relevantMutations = mutations.filter(mutation => {
                // Игнорираме промени в атрибутите на изображенията
                if (mutation.type === 'attributes' &&
                    (mutation.target.tagName === 'IMG' ||
                     mutation.target.classList.contains('product-image-placeholder'))) {
                    return false;
                }

                // Игнорираме промени в стила на елементите
                if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                    return false;
                }

                // Игнорираме промени в класовете на елементите
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    return false;
                }

                // Игнорираме промени в src атрибута на изображенията
                if (mutation.type === 'attributes' && mutation.attributeName === 'src') {
                    return false;
                }

                return true;
            });

            // Проверяваме дали има релевантни промени
            if (relevantMutations.length > 0) {
                // Маркираме, че обработваме промени
                isProcessingMutations = true;

                // Зареждаме изображенията
                loadProductImages(true);

                // След кратко забавяне, маркираме, че сме приключили с обработката
                setTimeout(() => {
                    isProcessingMutations = false;
                }, 500);
            }
        }
    });

    // Наблюдаваме промени само в основния контейнер с продукти
    const productsGrid = document.getElementById('products-grid');
    if (productsGrid) {
        observer.observe(productsGrid, {
            childList: true,
            subtree: false // Не наблюдаваме промени в дълбочина, за да избегнем безкрайни цикли
        });
    }

    // ПОДХОД 3: Използваме IntersectionObserver за следене на видимостта на елементите
    // Това е най-модерният и ефективен начин за следене на видимостта на елементи
    const imageContainers = document.querySelectorAll('.product-image-container');

    const intersectionObserver = new IntersectionObserver(function(entries) {
        let hasVisibleEntries = false;

        entries.forEach(entry => {
            if (entry.isIntersecting) {
                hasVisibleEntries = true;
            }
        });

        if (hasVisibleEntries && window.initialLoadComplete) {
            loadProductImages(true);
        }
    }, {
        root: null, // viewport
        rootMargin: '200px', // разширяваме областта с 200px във всички посоки
        threshold: 0.1 // 10% от елемента трябва да бъде видим
    });

    // Добавяме всички контейнери за наблюдение
    imageContainers.forEach(container => {
        intersectionObserver.observe(container);
    });

    // Зареждане на изображения при промяна на размера на прозореца
    // Използваме debounce, за да избегнем твърде много извиквания
    window.addEventListener('resize', debounce(function() {
        // Проверяваме дали първоначалното зареждане е завършено
        if (window.initialLoadComplete) {
            loadProductImages(true); // Подаваме true, за да укажем, че е събитие от промяна на размера
        }
    }, 300)); // Изчакваме 300ms след последната промяна на размера
});
</script>
