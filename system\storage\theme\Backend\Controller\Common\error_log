[30-Jun-2025 07:44:17 UTC] PHP Fatal error:  Uncaught Exception: Error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'pd.name LIKE '5000357%'
                            OR p.model LIKE '5000357%...' at line 10<br />Error No: 1064<br />
                    SELECT COUNT(*) as total
                    FROM
                        oc_product p
                    LEFT JOIN
                        oc_product_description pd ON (p.product_id = pd.product_id)
                    WHERE
                        pd.language_id = '2'
                        AND (
                            p.product_id LIKE '5000357%'
                            pd.name LIKE '5000357%'
                            OR p.model LIKE '5000357%'
                            OR p.sku LIKE '5000357%'
                        )
                 in /home/<USER>/theme25/system/library/db/mysqli.php:40
Stack trace:
#0 /home/<USER>/theme25/system/library/db.php(16): DB\MySQLi->query('\n              ...', A in /home/<USER>/theme25/system/library/db/mysqli.php on line 40
