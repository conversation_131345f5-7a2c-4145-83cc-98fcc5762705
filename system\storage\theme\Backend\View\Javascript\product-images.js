/**
 * JavaScript модул за управление на изображения в листинга на продукти
 * Отговаря за AJAX зареждане на изображения в листинга на продукти
 */
(function() {
    'use strict';

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        // Инициализиране на функционалността за изображения в листинга
        if (typeof BackendModule !== 'undefined') {
            BackendModule.initProductListImages();
        }
    });

    // Добавяне на функционалност към основния модул
    if (typeof BackendModule !== 'undefined') {
        Object.assign(BackendModule, {
            // Конфигурация за изображенията в листинга
            productListImages: {
                initialLoadComplete: false,
                lastScrollTop: 0,
                lastCheckTime: Date.now(),
                isProcessingMutations: false,
                placeholderBase64: "data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="
            },

            /**
             * Инициализация на функционалността за изображения в листинга
             */
            initProductListImages: function() {
                this.initImageLoading();
                this.initScrollHandlers();
                this.initResizeHandler();
                this.initMutationObserver();
                this.initIntersectionObserver();
                this.initFallbackTimer();
            },

            /**
             * Инициализация на основното зареждане на изображения
             */
            initImageLoading: function() {
                // Първоначално зареждане на видимите изображения
                this.loadProductImages(false);
                
                // Маркираме, че първоначалното зареждане е завършено
                this.productListImages.initialLoadComplete = true;
                window.initialLoadComplete = true;
            },

            /**
             * AJAX зареждане на изображения в листинга на продукти
             * @param {boolean} isScrollEvent - Дали е извикано от скролиране
             */
            loadProductImages: function(isScrollEvent = false) {
                // Получаваме текущата позиция на скролиране
                const mainContainer = document.querySelector('main.overflow-y-auto');
                const scrollTop = mainContainer ? mainContainer.scrollTop : (window.pageYOffset || document.documentElement.scrollTop);

                const imageContainers = document.querySelectorAll('.product-image-container');
                let loadedCount = 0;

                imageContainers.forEach(container => {
                    const productId = container.getAttribute('data-product-id');
                    const width = container.getAttribute('data-width');
                    const height = container.getAttribute('data-height');
                    const img = container.querySelector('img');

                    // Проверка дали изображението вече е заредено или е имало грешка при зареждането
                    if (img.src && img.src !== this.productListImages.placeholderBase64 && img.src !== window.location.href) {
                        return;
                    }

                    // Проверяваме дали контейнерът е маркиран като такъв с грешка при зареждане
                    if (container.hasAttribute('data-load-error')) {
                        return;
                    }

                    // Проверка дали контейнерът е видим във viewport
                    if (!this.isImageContainerVisible(container, isScrollEvent)) {
                        return;
                    }

                    // Изображението е видимо и ще бъде заредено
                    loadedCount++;
                    this.loadSingleProductImage(container, productId, width, height, img);
                });
            },

            /**
             * Проверява дали контейнерът на изображението е видим
             * @param {HTMLElement} container - Контейнерът на изображението
             * @param {boolean} isScrollEvent - Дали е извикано от скролиране
             * @returns {boolean} Дали е видим
             */
            isImageContainerVisible: function(container, isScrollEvent) {
                const rect = container.getBoundingClientRect();
                const viewportHeight = window.innerHeight || document.documentElement.clientHeight;
                const mainContainer = document.querySelector('main.overflow-y-auto');

                // Изчисляваме позицията спрямо main контейнера, ако съществува
                let relativeTop = rect.top;
                let relativeBottom = rect.bottom;

                if (mainContainer) {
                    const mainRect = mainContainer.getBoundingClientRect();
                    relativeTop = rect.top - mainRect.top;
                    relativeBottom = rect.bottom - mainRect.top;
                }

                let isVisible;

                if (!isScrollEvent) {
                    // При първоначално зареждане - строга проверка
                    if (mainContainer) {
                        isVisible = (
                            relativeTop >= 0 &&
                            relativeTop < mainContainer.clientHeight &&
                            relativeBottom > 0
                        );
                    } else {
                        isVisible = (
                            rect.top >= 0 &&
                            rect.top < viewportHeight &&
                            rect.bottom > 0
                        );
                    }
                } else {
                    // При скролиране - либерална проверка с буфер
                    const buffer = 1000;

                    if (mainContainer) {
                        isVisible = (
                            relativeTop < mainContainer.clientHeight + buffer &&
                            relativeBottom > -buffer
                        );
                    } else {
                        isVisible = (
                            rect.top < viewportHeight + buffer &&
                            rect.bottom > -buffer
                        );
                    }
                }

                return isVisible;
            },

            /**
             * Зарежда единично изображение на продукт
             * @param {HTMLElement} container - Контейнерът на изображението
             * @param {string} productId - ID на продукта
             * @param {string} width - Ширина на изображението
             * @param {string} height - Височина на изображението
             * @param {HTMLElement} img - IMG елементът
             */
            loadSingleProductImage: function(container, productId, width, height, img) {
                // AJAX заявка за зареждане на изображението
                const baseUrl = window.location.href.split('index.php')[0];
                const urlParams = new URLSearchParams(window.location.search);
                const userToken = urlParams.get('user_token') || '';

                fetch(`${baseUrl}index.php?route=tool/image&user_token=${userToken}&product_id=${productId}&width=${width}&height=${height}`, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('HTTP error ' + response.status);
                    }
                    return response.json();
                })
                .then(data => {
                    this.handleImageLoadSuccess(data, img, container);
                })
                .catch(error => {
                    this.handleImageLoadError(error, img, container);
                });
            },

            /**
             * Обработва успешно зареждане на изображение
             * @param {Object} data - Данните от сървъра
             * @param {HTMLElement} img - IMG елементът
             * @param {HTMLElement} container - Контейнерът на изображението
             */
            handleImageLoadSuccess: function(data, img, container) {
                if (data.success && data.image) {
                    // Проверка дали URL адресът е абсолютен
                    if (data.image.indexOf('http') !== 0) {
                        const baseUrl = window.location.href.split('index.php')[0];
                        img.src = baseUrl + data.image.replace(/^\//, '');
                    } else {
                        img.src = data.image;
                    }

                    img.onload = () => {
                        img.classList.remove('opacity-0');
                        const placeholder = container.querySelector('.product-image-placeholder');
                        const errorElement = container.querySelector('.product-image-error');
                        if (placeholder) placeholder.style.display = 'none';
                        if (errorElement) errorElement.classList.add('hidden');
                    };

                    img.onerror = () => {
                        this.setFallbackImage(img, container);
                    };
                } else {
                    this.setFallbackImage(img, container, data.image);
                }
            },

            /**
             * Обработва грешка при зареждане на изображение
             * @param {Error} error - Грешката
             * @param {HTMLElement} img - IMG елементът
             * @param {HTMLElement} container - Контейнерът на изображението
             */
            handleImageLoadError: function(error, img, container) {
                // Показваме съобщение за грешка и маркираме контейнера
                const placeholder = container.querySelector('.product-image-placeholder');
                const errorElement = container.querySelector('.product-image-error');
                
                if (placeholder) placeholder.style.display = 'none';
                if (errorElement) errorElement.classList.remove('hidden');

                // Маркираме контейнера, че е имало грешка при зареждане
                container.setAttribute('data-load-error', 'true');

                // Скриваме изображението
                img.classList.add('opacity-0');
            },

            /**
             * Задава fallback изображение
             * @param {HTMLElement} img - IMG елементът
             * @param {HTMLElement} container - Контейнерът на изображението
             * @param {string} fallbackImage - Fallback изображение от сървъра
             */
            setFallbackImage: function(img, container, fallbackImage = null) {
                if (fallbackImage && fallbackImage.indexOf('http') !== 0) {
                    const baseUrl = window.location.href.split('index.php')[0];
                    img.src = baseUrl + fallbackImage.replace(/^\//, '');
                } else if (fallbackImage) {
                    img.src = fallbackImage;
                } else {
                    img.src = 'image/no_image.png';
                }

                img.onload = () => {
                    img.classList.remove('opacity-0');
                    const placeholder = container.querySelector('.product-image-placeholder');
                    const errorElement = container.querySelector('.product-image-error');
                    if (placeholder) placeholder.style.display = 'none';
                    if (errorElement) errorElement.classList.add('hidden');
                };

                img.onerror = () => {
                    img.src = 'image/no_image.png';
                    img.classList.remove('opacity-0');
                    const placeholder = container.querySelector('.product-image-placeholder');
                    const errorElement = container.querySelector('.product-image-error');
                    if (placeholder) placeholder.style.display = 'none';
                    if (errorElement) errorElement.classList.add('hidden');
                    container.setAttribute('data-load-error', 'true');
                };
            },

            /**
             * Инициализация на обработчиците за скролиране
             */
            initScrollHandlers: function() {
                // Функция за throttle на събитията
                const throttledLoadImages = this.throttle(() => {
                    if (this.productListImages.initialLoadComplete) {
                        this.loadProductImages(true);
                    }
                }, 200);

                // Намираме main контейнера, който има скролиране
                const mainContainer = document.querySelector('main.overflow-y-auto');

                // Различни обработчици за скролиране (резервни варианти)
                if (mainContainer) {
                    mainContainer.addEventListener('scroll', throttledLoadImages);
                }
                window.addEventListener('scroll', throttledLoadImages);
                window.addEventListener('scroll', throttledLoadImages, true);
                document.addEventListener('scroll', throttledLoadImages);
                document.body.addEventListener('scroll', throttledLoadImages);

                // Обработчик на wheel събитие
                window.addEventListener('wheel', this.throttle(() => {
                    if (this.productListImages.initialLoadComplete) {
                        this.loadProductImages(true);
                    }
                }, 300));

                // Обработчик на touchmove събитие (за мобилни устройства)
                window.addEventListener('touchmove', this.throttle(() => {
                    if (this.productListImages.initialLoadComplete) {
                        this.loadProductImages(true);
                    }
                }, 300));

                // Периодична проверка за скролиране
                this.initPeriodicScrollCheck();
            },

            /**
             * Инициализация на периодична проверка за скролиране
             */
            initPeriodicScrollCheck: function() {
                setInterval(() => {
                    const now = Date.now();
                    if (now - this.productListImages.lastCheckTime < 200) {
                        return;
                    }

                    this.productListImages.lastCheckTime = now;

                    // Получаваме текущата позиция на скролиране от main контейнера
                    const mainContainer = document.querySelector('main.overflow-y-auto');
                    const scrollTop = mainContainer ? mainContainer.scrollTop : (window.pageYOffset || document.documentElement.scrollTop);

                    // Проверяваме дали има значителна промяна в позицията на скролиране
                    if (Math.abs(scrollTop - this.productListImages.lastScrollTop) > 10) {
                        this.productListImages.lastScrollTop = scrollTop;

                        if (this.productListImages.initialLoadComplete) {
                            this.loadProductImages(true);
                        }
                    }
                }, 200);
            },

            /**
             * Инициализация на обработчика за промяна на размера на прозореца
             */
            initResizeHandler: function() {
                window.addEventListener('resize', this.debounce(() => {
                    if (this.productListImages.initialLoadComplete) {
                        this.loadProductImages(true);
                    }
                }, 300));
            },

            /**
             * Инициализация на MutationObserver за следене на промени в DOM
             */
            initMutationObserver: function() {
                const observer = new MutationObserver((mutations) => {
                    if (!this.productListImages.isProcessingMutations && this.productListImages.initialLoadComplete) {
                        // Филтрираме мутациите, за да избегнем безкрайни цикли
                        const relevantMutations = mutations.filter(mutation => {
                            // Игнорираме промени в атрибутите на изображенията
                            if (mutation.type === 'attributes' &&
                                (mutation.target.tagName === 'IMG' ||
                                 mutation.target.classList.contains('product-image-placeholder'))) {
                                return false;
                            }

                            // Игнорираме промени в стила и класовете
                            if (mutation.type === 'attributes' &&
                                (mutation.attributeName === 'style' ||
                                 mutation.attributeName === 'class' ||
                                 mutation.attributeName === 'src')) {
                                return false;
                            }

                            return true;
                        });

                        if (relevantMutations.length > 0) {
                            this.productListImages.isProcessingMutations = true;
                            this.loadProductImages(true);

                            setTimeout(() => {
                                this.productListImages.isProcessingMutations = false;
                            }, 500);
                        }
                    }
                });

                // Наблюдаваме промени само в основния контейнер с продукти
                const productsGrid = document.getElementById('products-grid');
                if (productsGrid) {
                    observer.observe(productsGrid, {
                        childList: true,
                        subtree: false
                    });
                }
            },

            /**
             * Инициализация на IntersectionObserver за следене на видимостта
             */
            initIntersectionObserver: function() {
                const imageContainers = document.querySelectorAll('.product-image-container');

                const intersectionObserver = new IntersectionObserver((entries) => {
                    let hasVisibleEntries = false;

                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            hasVisibleEntries = true;
                        }
                    });

                    if (hasVisibleEntries && this.productListImages.initialLoadComplete) {
                        this.loadProductImages(true);
                    }
                }, {
                    root: null,
                    rootMargin: '200px',
                    threshold: 0.1
                });

                // Добавяме всички контейнери за наблюдение
                imageContainers.forEach(container => {
                    intersectionObserver.observe(container);
                });
            },

            /**
             * Инициализация на fallback таймер за принудително зареждане
             */
            initFallbackTimer: function() {
                // Принудително зареждане на всички изображения след 3 секунди
                setTimeout(() => {
                    const allImageContainers = document.querySelectorAll('.product-image-container');
                    allImageContainers.forEach(container => {
                        const productId = container.getAttribute('data-product-id');
                        const width = container.getAttribute('data-width');
                        const height = container.getAttribute('data-height');
                        const img = container.querySelector('img');

                        // Проверка дали изображението вече е заредено
                        if (img.src && img.src !== this.productListImages.placeholderBase64 && img.src !== window.location.href) {
                            return;
                        }

                        // Проверяваме дали контейнерът е маркиран като такъв с грешка
                        if (container.hasAttribute('data-load-error')) {
                            return;
                        }

                        this.loadSingleProductImage(container, productId, width, height, img);
                    });
                }, 3000);
            },

            /**
             * Функция за throttle на събитията
             * @param {Function} func - Функцията за изпълнение
             * @param {number} limit - Лимитът в милисекунди
             * @returns {Function} Throttled функция
             */
            throttle: function(func, limit) {
                let inThrottle;
                return function() {
                    const context = this;
                    const args = arguments;
                    if (!inThrottle) {
                        func.apply(context, args);
                        inThrottle = true;
                        setTimeout(() => inThrottle = false, limit);
                    }
                };
            },

            /**
             * Функция за debounce на събитията
             * @param {Function} func - Функцията за изпълнение
             * @param {number} wait - Времето за изчакване в милисекунди
             * @returns {Function} Debounced функция
             */
            debounce: function(func, wait) {
                let timeout;
                return function() {
                    const context = this;
                    const args = arguments;
                    clearTimeout(timeout);
                    timeout = setTimeout(function() {
                        func.apply(context, args);
                    }, wait);
                };
            }
        });
    }

    // Експортиране на модула като глобален обект
    window.ProductImages = {
        initProductListImages: function() {
            if (typeof BackendModule !== 'undefined') {
                BackendModule.initProductListImages();
            }
        }
    };
})();
