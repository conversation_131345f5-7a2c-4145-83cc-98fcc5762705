{% if categories %}
    <div class="p-6">
        <div class="space-y-4">
            {% for category in categories %}
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 hover:bg-gray-100 transition-colors">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        {# <input type="checkbox" name="selected[]" value="{{ category.category_id }}" class="rounded"> #}
                        <div class="flex items-center space-x-2">
                            {# <i class="ri-drag-move-line text-gray-400 cursor-move"></i> #}
                            <div class="w-12 h-12 bg-white border border-gray-200 rounded-lg flex items-center justify-center">
                                {% if category.image %}
                                    <img src="{{ category.image }}" alt="{{ category.name }}" class="w-10 h-10 object-cover rounded">
                                {% else %}
                                    <i class="ri-folder-line text-gray-400"></i>
                                {% endif %}
                            </div>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">
                                {{ category.name }}
                                {% if category.parent_id > 0 %}
                                    <span class="text-xs text-gray-500 ml-2">(Подкатегория)</span>
                                {% endif %}
                            </h4>
                            <div class="text-xs text-gray-500">
                                ID: {{ category.category_id }} | Сортиране: {{ category.sort_order|default(0) }}
                                {% if category.date_added %}
                                    | Добавена: {{ category.date_added|date('d.m.Y') }}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ category.status_class }}">
                            {{ category.status }}
                        </span>
                        <div class="flex space-x-2">
                            {# <a href="{{ category.view }}" class="text-blue-600 hover:text-blue-900" title="Преглед">
                                <i class="ri-eye-line"></i>
                            </a> #}
                            <a href="{{ category.edit }}" class="text-primary hover:text-primary/80" title="Редактирай">
                                <i class="ri-edit-line"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
{% else %}
    <div class="text-center py-8">
        <i class="ri-folder-line text-gray-400 text-4xl mb-4"></i>
        <p class="text-gray-500">Няма намерени категории</p>
    </div>
{% endif %}
